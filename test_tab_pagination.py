#!/usr/bin/env python3
"""
Test script for the enhanced CustomerPlatformIdentityListView with tab-based pagination.
This script tests the new tab filtering functionality.
"""

import os
import sys
import django
import requests
from django.conf import settings

# Add the project root to Python path
sys.path.append('/Users/<USER>/Developer/aibl-salmate/Salmate')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salmate.settings')
django.setup()

def test_tab_pagination():
    """Test the tab-based pagination functionality"""
    
    # Base URL for the API endpoint
    base_url = "http://localhost:8000/customer/api/platform-identities/"
    
    # Test parameters
    test_cases = [
        {
            'name': 'Test small page size (2 items)',
            'params': {'page': 1, 'page_size': 2}
        },
        {
            'name': 'Test my-assigned tab',
            'params': {'page': 1, 'page_size': 2, 'tab': 'my-assigned', 'current_user': '<PERSON>'}
        },
        {
            'name': 'Test my-closed tab',
            'params': {'page': 1, 'page_size': 2, 'tab': 'my-closed', 'current_user': '<PERSON> <PERSON>e'}
        },
        {
            'name': 'Test open tab',
            'params': {'page': 1, 'page_size': 2, 'tab': 'open', 'current_user': 'John Doe'}
        },
        {
            'name': 'Test others-assigned tab',
            'params': {'page': 1, 'page_size': 2, 'tab': 'others-assigned', 'current_user': 'John Doe'}
        },
        {
            'name': 'Test minimum page size (1 item)',
            'params': {'page': 1, 'page_size': 1}
        }
    ]
    
    print("Testing Enhanced CustomerPlatformIdentityListView")
    print("=" * 60)
    
    for test_case in test_cases:
        print(f"\n{test_case['name']}")
        print("-" * 40)
        
        try:
            response = requests.get(base_url, params=test_case['params'])
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                count = data.get('count', 0)
                next_page = data.get('next')
                
                print(f"✅ Status: {response.status_code}")
                print(f"📊 Total count: {count}")
                print(f"📄 Results returned: {len(results)}")
                print(f"➡️  Next page: {'Yes' if next_page else 'No'}")
                
                # Show sample data for tab filtering tests
                if 'tab' in test_case['params'] and results:
                    print(f"🔍 Sample results for tab '{test_case['params']['tab']}':")
                    for i, result in enumerate(results[:2]):  # Show first 2 results
                        status = result.get('latest_ticket_status', 'N/A')
                        owner = result.get('latest_ticket_owner', 'N/A')
                        platform_id = result.get('id', 'N/A')
                        print(f"   {i+1}. ID: {platform_id}, Status: {status}, Owner: {owner}")
                
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

def test_pagination_class():
    """Test the PlatformIdentityPagination class directly"""
    from customer.views import PlatformIdentityPagination
    from django.http import HttpRequest
    
    print("\n\nTesting PlatformIdentityPagination Class")
    print("=" * 60)
    
    pagination = PlatformIdentityPagination()
    
    # Test default page size
    print(f"Default page size: {pagination.page_size}")
    
    # Test with different page_size parameters
    test_sizes = [1, 2, 5, 10, 50, 100, 250]  # 250 should be capped at 200
    
    for size in test_sizes:
        request = HttpRequest()
        request.GET = {'page_size': str(size)}
        
        actual_size = pagination.get_page_size(request)
        print(f"Requested: {size}, Actual: {actual_size}")

if __name__ == "__main__":
    print("🚀 Starting Tab Pagination Tests")
    
    # Test pagination class
    test_pagination_class()
    
    # Test API endpoints (requires running Django server)
    print("\n" + "="*60)
    print("⚠️  API Endpoint Tests (requires Django server running on localhost:8000)")
    
    try:
        test_tab_pagination()
    except Exception as e:
        print(f"❌ API tests failed: {e}")
        print("💡 Make sure Django server is running: python manage.py runserver")
    
    print("\n🎉 Tests completed!")
